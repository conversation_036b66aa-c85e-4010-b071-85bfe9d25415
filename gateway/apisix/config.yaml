#
# Apache APISIX Declarative Configuration
# This configuration defines routes and upstreams for the Cortexa platform
#

# Define upstreams (backend services)
upstreams:
  - id: voice-gateway
    name: voice-gateway-service
    desc: Voice Gateway WebSocket Service
    type: roundrobin
    scheme: http
    nodes:
      - host: voice-gateway
        port: 8002
        weight: 1
    timeout:
      connect: 6
      send: 6
      read: 60
    keepalive_pool:
      size: 320
      idle_timeout: 60
      requests: 1000
    health_checker:
      active:
        type: http
        http_path: /health
        healthy:
          interval: 5
          http_statuses: [200, 302]
          successes: 2
        unhealthy:
          interval: 5
          http_statuses: [429, 404, 500, 501, 502, 503, 504, 505]
          http_failures: 3
          tcp_failures: 3

# Define routes
routes:
  - id: voice-gateway-websocket
    name: voice-gateway-websocket-route
    desc: Route for Voice Gateway WebSocket connections with JWT authentication
    uri: /v1/call/*
    methods: ["GET"]
    enable_websocket: true
    upstream_id: voice-gateway
    plugins:
      # JWT Authentication using custom Python plugin
      # This runs during the initial WebSocket handshake ($connect)
      ext-plugin-pre-req:
        conf:
          - name: "jwt-authorizer"
            value: |
              {
                "jwks_url": "https://your-supabase-project.supabase.co/auth/v1/jwks",
                "issuer": "https://your-supabase-project.supabase.co/auth/v1",
                "audience": "authenticated",
                "algorithms": ["RS256"],
                "cache_ttl": 3600,
                "max_retries": 3,
                "required_claims": ["sub", "email", "role"],
                "claim_validations": {
                  "aud": "authenticated",
                  "role": ["authenticated"]
                }
              }
      # Enable CORS for WebSocket connections
      cors:
        allow_origins: "*"
        allow_methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"]
        allow_headers: ["Authorization", "Content-Type", "X-Requested-With"]
        allow_credentials: true
        max_age: 5
      # Add request/response logging with user context
      http-logger:
        uri: "http://log-collector:8080/logs"
        timeout: 3
        name: "voice-gateway-logger"
        max_retry_count: 0
        retry_delay: 1
        buffer_duration: 60
        inactive_timeout: 10
        batch_max_size: 1000
        include_req_body: false
        include_resp_body: false
        concat_method: "json"
      # Add prometheus metrics
      prometheus:
        prefer_name: true
      # Add rate limiting per authenticated user
      limit-req:
        rate: 10
        burst: 20
        rejected_code: 429
        key: "remote_addr"
        key_type: "var"

  - id: voice-gateway-health
    name: voice-gateway-health-route
    desc: Route for Voice Gateway health checks
    uri: /health
    methods: ["GET"]
    upstream_id: voice-gateway
    plugins:
      # Add prometheus metrics
      prometheus:
        prefer_name: true

#END_CONF