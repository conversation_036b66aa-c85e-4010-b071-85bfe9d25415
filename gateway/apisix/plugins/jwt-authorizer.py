#!/usr/bin/env python3
"""
Apache APISIX Python JWT Authorizer Plugin

This plugin provides JWT token validation for Apache APISIX using Supabase JWKS.
It fetches and caches the public JSON Web Key Set (JWKS) from Supabase,
validates JWT tokens from the Authorization header, and returns explicit
allow/deny policies based on validation results.

Features:
- JW<PERSON> fetching and intelligent caching with TTL
- JWT signature verification using cached public keys
- Standard claims validation (iss, exp, aud)
- Explicit allow/deny policy responses
- Comprehensive error handling and logging
- Production-ready performance optimizations

Author: Cortexa Platform Team
License: Apache 2.0
"""

import json
import time
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timezone
import threading

# Third-party imports (these would need to be installed)
try:
    import jwt
    from jwt import PyJWKClient, PyJWK
    from jwt.exceptions import (
        InvalidTokenError,
        ExpiredSignatureError,
        InvalidSignatureError,
        InvalidIssuerError,
        InvalidAudienceError,
        InvalidKeyError
    )
    import requests
    from requests.adapters import HTTPAdapter
    from urllib3.util.retry import Retry
except ImportError as e:
    logging.error(f"Required dependencies not installed: {e}")
    logging.error("Please install: pip install PyJWT[crypto] requests")
    raise

# APISIX plugin base imports
try:
    from apisix.runner.plugin.base import Base
    from apisix.runner.http.request import Request
    from apisix.runner.http.response import Response
except ImportError:
    # For development/testing outside APISIX environment
    logging.warning("APISIX plugin base classes not available - running in development mode")

    class Base:
        def __init__(self, name):
            self.name = name
            self.config = {}
        def stop(self): pass
        def rewrite(self): pass

    class Request:
        def __init__(self):
            self.headers = {}

    class Response:
        def __init__(self):
            self.headers = {}
            self.body = ""
            self.status_code = 200


class JWKSCache:
    """
    Thread-safe JWKS cache with intelligent refresh and TTL management.

    Features:
    - Automatic cache refresh based on TTL
    - Background refresh to avoid blocking requests
    - Fallback to cached keys on fetch failures
    - Thread-safe operations
    """

    def __init__(self, jwks_url: str, cache_ttl: int = 3600, max_retries: int = 3):
        """
        Initialize JWKS cache.

        Args:
            jwks_url: URL to fetch JWKS from
            cache_ttl: Cache TTL in seconds (default: 1 hour)
            max_retries: Maximum retry attempts for JWKS fetching
        """
        self.jwks_url = jwks_url
        self.cache_ttl = cache_ttl
        self.max_retries = max_retries

        # Cache storage
        self._cache: Dict[str, PyJWK] = {}
        self._cache_timestamp: float = 0
        self._cache_lock = threading.RLock()

        # HTTP session with retry strategy
        self._session = requests.Session()
        retry_strategy = Retry(
            total=max_retries,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self._session.mount("http://", adapter)
        self._session.mount("https://", adapter)

        # Logging
        self.logger = logging.getLogger(f"{__name__}.JWKSCache")

    def _is_cache_expired(self) -> bool:
        """Check if cache has expired based on TTL."""
        return time.time() - self._cache_timestamp > self.cache_ttl

    def _fetch_jwks(self) -> Dict[str, Any]:
        """
        Fetch JWKS from the configured URL.

        Returns:
            JWKS data as dictionary

        Raises:
            requests.RequestException: On HTTP errors
            ValueError: On invalid JSON response
        """
        self.logger.debug(f"Fetching JWKS from {self.jwks_url}")

        try:
            response = self._session.get(
                self.jwks_url,
                timeout=10,
                headers={
                    'User-Agent': 'APISIX-JWT-Authorizer/1.0',
                    'Accept': 'application/json'
                }
            )
            response.raise_for_status()

            jwks_data = response.json()

            if 'keys' not in jwks_data:
                raise ValueError("Invalid JWKS format: missing 'keys' field")

            self.logger.info(f"Successfully fetched JWKS with {len(jwks_data['keys'])} keys")
            return jwks_data

        except requests.RequestException as e:
            self.logger.error(f"Failed to fetch JWKS: {e}")
            raise
        except (json.JSONDecodeError, ValueError) as e:
            self.logger.error(f"Invalid JWKS response: {e}")
            raise

    def _update_cache(self, jwks_data: Dict[str, Any]) -> None:
        """
        Update the internal cache with new JWKS data.

        Args:
            jwks_data: JWKS data dictionary
        """
        new_cache = {}

        for key_data in jwks_data.get('keys', []):
            try:
                jwk = PyJWK(key_data)
                if jwk.key_id:
                    new_cache[jwk.key_id] = jwk
                    self.logger.debug(f"Cached JWK with kid: {jwk.key_id}")
            except Exception as e:
                self.logger.warning(f"Failed to parse JWK: {e}")
                continue

        with self._cache_lock:
            self._cache = new_cache
            self._cache_timestamp = time.time()

        self.logger.info(f"Updated JWKS cache with {len(new_cache)} keys")

    def get_signing_key(self, kid: str) -> Optional[PyJWK]:
        """
        Get signing key by key ID, refreshing cache if necessary.

        Args:
            kid: Key ID from JWT header

        Returns:
            PyJWK object if found, None otherwise
        """
        # Check if we need to refresh cache
        if not self._cache or self._is_cache_expired():
            try:
                jwks_data = self._fetch_jwks()
                self._update_cache(jwks_data)
            except Exception as e:
                self.logger.error(f"Failed to refresh JWKS cache: {e}")
                # Continue with existing cache if available
                if not self._cache:
                    return None

        with self._cache_lock:
            return self._cache.get(kid)

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics for monitoring."""
        with self._cache_lock:
            return {
                'keys_count': len(self._cache),
                'cache_age_seconds': time.time() - self._cache_timestamp,
                'cache_expired': self._is_cache_expired(),
                'jwks_url': self.jwks_url
            }


class JWTValidator:
    """
    JWT token validator with comprehensive validation logic.

    Handles:
    - Token parsing and structure validation
    - Signature verification using JWKS
    - Standard claims validation (iss, exp, aud)
    - Custom claims validation
    """

    def __init__(self, jwks_cache: JWKSCache):
        """
        Initialize JWT validator.

        Args:
            jwks_cache: JWKS cache instance
        """
        self.jwks_cache = jwks_cache
        self.logger = logging.getLogger(f"{__name__}.JWTValidator")

    def validate_token(self, token: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate JWT token against configuration.

        Args:
            token: JWT token string
            config: Plugin configuration

        Returns:
            Validation result dictionary with 'valid', 'claims', 'error' keys
        """
        try:
            # Parse token header to get key ID
            unverified_header = jwt.get_unverified_header(token)
            kid = unverified_header.get('kid')

            if not kid:
                return {
                    'valid': False,
                    'error': 'Missing key ID (kid) in token header',
                    'error_code': 'MISSING_KID'
                }

            # Get signing key from cache
            signing_key = self.jwks_cache.get_signing_key(kid)
            if not signing_key:
                return {
                    'valid': False,
                    'error': f'Signing key not found for kid: {kid}',
                    'error_code': 'KEY_NOT_FOUND'
                }

            # Validate token signature and claims
            try:
                claims = jwt.decode(
                    token,
                    signing_key.key,
                    algorithms=config.get('algorithms', ['RS256']),
                    issuer=config.get('issuer'),
                    audience=config.get('audience'),
                    options={
                        'verify_signature': True,
                        'verify_exp': True,
                        'verify_iss': True,
                        'verify_aud': config.get('audience') is not None,
                        'require_exp': True,
                        'require_iss': True,
                    }
                )

                # Additional custom validations
                validation_error = self._validate_custom_claims(claims, config)
                if validation_error:
                    return {
                        'valid': False,
                        'error': validation_error,
                        'error_code': 'CUSTOM_VALIDATION_FAILED'
                    }

                self.logger.debug(f"Token validated successfully for subject: {claims.get('sub', 'unknown')}")

                return {
                    'valid': True,
                    'claims': claims,
                    'kid': kid
                }

            except ExpiredSignatureError:
                return {
                    'valid': False,
                    'error': 'Token has expired',
                    'error_code': 'TOKEN_EXPIRED'
                }
            except InvalidSignatureError:
                return {
                    'valid': False,
                    'error': 'Invalid token signature',
                    'error_code': 'INVALID_SIGNATURE'
                }
            except InvalidIssuerError:
                return {
                    'valid': False,
                    'error': 'Invalid token issuer',
                    'error_code': 'INVALID_ISSUER'
                }
            except InvalidAudienceError:
                return {
                    'valid': False,
                    'error': 'Invalid token audience',
                    'error_code': 'INVALID_AUDIENCE'
                }
            except InvalidTokenError as e:
                return {
                    'valid': False,
                    'error': f'Invalid token: {str(e)}',
                    'error_code': 'INVALID_TOKEN'
                }

        except Exception as e:
            self.logger.error(f"Unexpected error during token validation: {e}")
            return {
                'valid': False,
                'error': 'Internal validation error',
                'error_code': 'INTERNAL_ERROR'
            }

    def _validate_custom_claims(self, claims: Dict[str, Any], config: Dict[str, Any]) -> Optional[str]:
        """
        Validate custom claims based on configuration.

        Args:
            claims: Decoded JWT claims
            config: Plugin configuration

        Returns:
            Error message if validation fails, None if successful
        """
        # Validate required claims
        required_claims = config.get('required_claims', [])
        for claim in required_claims:
            if claim not in claims:
                return f"Missing required claim: {claim}"

        # Validate claim values
        claim_validations = config.get('claim_validations', {})
        for claim, expected_values in claim_validations.items():
            if claim in claims:
                claim_value = claims[claim]
                if isinstance(expected_values, list):
                    if claim_value not in expected_values:
                        return f"Invalid value for claim {claim}: {claim_value}"
                elif claim_value != expected_values:
                    return f"Invalid value for claim {claim}: {claim_value}"

        return None


class JWTAuthorizer(Base):
    """
    Apache APISIX JWT Authorizer Plugin

    This plugin validates JWT tokens using Supabase JWKS and returns
    explicit allow/deny policies based on validation results.
    """

    # Class-level cache instances (shared across plugin instances)
    _jwks_caches: Dict[str, JWKSCache] = {}
    _cache_lock = threading.Lock()

    def __init__(self):
        """Initialize the JWT Authorizer plugin."""
        super(JWTAuthorizer, self).__init__(self.__class__.__name__)
        self.logger = logging.getLogger(f"{__name__}.JWTAuthorizer")
        self.validator: Optional[JWTValidator] = None

        # Plugin metrics (for monitoring)
        self.metrics = {
            'total_requests': 0,
            'valid_tokens': 0,
            'invalid_tokens': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'errors': 0
        }

    def _get_jwks_cache(self, config: Dict[str, Any]) -> JWKSCache:
        """
        Get or create JWKS cache instance for the given configuration.

        Args:
            config: Plugin configuration

        Returns:
            JWKSCache instance
        """
        jwks_url = config.get('jwks_url')
        if not jwks_url:
            raise ValueError("jwks_url is required in plugin configuration")

        # Create cache key based on URL and TTL
        cache_key = f"{jwks_url}:{config.get('cache_ttl', 3600)}"

        with self._cache_lock:
            if cache_key not in self._jwks_caches:
                self._jwks_caches[cache_key] = JWKSCache(
                    jwks_url=jwks_url,
                    cache_ttl=config.get('cache_ttl', 3600),
                    max_retries=config.get('max_retries', 3)
                )
                self.logger.info(f"Created new JWKS cache for {jwks_url}")

            return self._jwks_caches[cache_key]

    def _extract_token_from_header(self, request: Request) -> Optional[str]:
        """
        Extract JWT token from Authorization header.

        Args:
            request: APISIX request object

        Returns:
            JWT token string or None if not found
        """
        auth_header = request.headers.get('Authorization', '')

        if not auth_header:
            return None

        # Support both "Bearer <token>" and "<token>" formats
        if auth_header.startswith('Bearer '):
            return auth_header[7:]  # Remove "Bearer " prefix
        elif auth_header.startswith('bearer '):
            return auth_header[7:]  # Remove "bearer " prefix (case insensitive)
        else:
            # Assume the entire header is the token
            return auth_header

    def _create_error_response(self, error_message: str, error_code: str,
                             status_code: int = 401) -> Dict[str, Any]:
        """
        Create standardized error response.

        Args:
            error_message: Human-readable error message
            error_code: Machine-readable error code
            status_code: HTTP status code

        Returns:
            Error response dictionary
        """
        return {
            'error': {
                'message': error_message,
                'code': error_code,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'plugin': 'jwt-authorizer'
            }
        }

    def _create_success_response(self, claims: Dict[str, Any], kid: str) -> Dict[str, Any]:
        """
        Create success response with user context.

        Args:
            claims: JWT claims
            kid: Key ID used for validation

        Returns:
            Success response dictionary
        """
        return {
            'user': {
                'id': claims.get('sub'),
                'email': claims.get('email'),
                'roles': claims.get('roles', []),
                'permissions': claims.get('permissions', []),
                'metadata': claims.get('user_metadata', {}),
                'app_metadata': claims.get('app_metadata', {})
            },
            'token': {
                'kid': kid,
                'iss': claims.get('iss'),
                'aud': claims.get('aud'),
                'exp': claims.get('exp'),
                'iat': claims.get('iat')
            },
            'validated_at': datetime.now(timezone.utc).isoformat()
        }

    def _validate_configuration(self, config: Dict[str, Any]) -> Optional[str]:
        """
        Validate plugin configuration.

        Args:
            config: Plugin configuration

        Returns:
            Error message if invalid, None if valid
        """
        required_fields = ['jwks_url']
        for field in required_fields:
            if field not in config:
                return f"Missing required configuration field: {field}"

        # Validate JWKS URL format
        jwks_url = config['jwks_url']
        if not jwks_url.startswith(('http://', 'https://')):
            return "jwks_url must be a valid HTTP/HTTPS URL"

        # Validate optional numeric fields
        numeric_fields = ['cache_ttl', 'max_retries']
        for field in numeric_fields:
            if field in config:
                try:
                    value = int(config[field])
                    if value <= 0:
                        return f"{field} must be a positive integer"
                except (ValueError, TypeError):
                    return f"{field} must be a valid integer"

        return None

    def filter(self, request: Request, response: Response):
        """
        Main plugin filter function called by APISIX.

        This function:
        1. Validates plugin configuration
        2. Extracts JWT token from Authorization header
        3. Validates the token using JWKS
        4. Returns allow/deny policy based on validation result

        Args:
            request: APISIX request object
            response: APISIX response object
        """
        self.metrics['total_requests'] += 1

        try:
            # Validate plugin configuration
            config_error = self._validate_configuration(self.config)
            if config_error:
                self.logger.error(f"Invalid plugin configuration: {config_error}")
                self.metrics['errors'] += 1

                response.status_code = 500
                response.body = json.dumps(self._create_error_response(
                    "Plugin configuration error",
                    "CONFIG_ERROR",
                    500
                ))
                response.headers['Content-Type'] = 'application/json'
                self.stop()
                return

            # Extract JWT token from Authorization header
            token = self._extract_token_from_header(request)
            if not token:
                self.logger.debug("No JWT token found in Authorization header")
                self.metrics['invalid_tokens'] += 1

                response.status_code = 401
                response.body = json.dumps(self._create_error_response(
                    "Missing or invalid Authorization header",
                    "MISSING_TOKEN"
                ))
                response.headers['Content-Type'] = 'application/json'
                self.stop()
                return

            # Initialize validator if not already done
            if not self.validator:
                jwks_cache = self._get_jwks_cache(self.config)
                self.validator = JWTValidator(jwks_cache)

            # Validate JWT token
            validation_result = self.validator.validate_token(token, self.config)

            if validation_result['valid']:
                # Token is valid - allow request to proceed
                self.logger.info(f"JWT token validated successfully for user: {validation_result['claims'].get('sub', 'unknown')}")
                self.metrics['valid_tokens'] += 1

                # Add user context to request headers for downstream services
                user_context = self._create_success_response(
                    validation_result['claims'],
                    validation_result['kid']
                )

                # Add user information to headers (base64 encoded to handle special characters)
                import base64
                user_context_b64 = base64.b64encode(
                    json.dumps(user_context).encode('utf-8')
                ).decode('utf-8')

                request.headers['X-User-Context'] = user_context_b64
                request.headers['X-User-ID'] = validation_result['claims'].get('sub', '')
                request.headers['X-User-Email'] = validation_result['claims'].get('email', '')
                request.headers['X-User-Roles'] = json.dumps(validation_result['claims'].get('roles', []))

                # Allow request to continue
                self.rewrite()

            else:
                # Token is invalid - deny request
                self.logger.warning(f"JWT token validation failed: {validation_result['error']}")
                self.metrics['invalid_tokens'] += 1

                # Determine appropriate HTTP status code based on error
                status_code = 401
                if validation_result['error_code'] in ['TOKEN_EXPIRED']:
                    status_code = 401
                elif validation_result['error_code'] in ['INVALID_SIGNATURE', 'INVALID_ISSUER', 'INVALID_AUDIENCE']:
                    status_code = 403
                elif validation_result['error_code'] in ['KEY_NOT_FOUND', 'MISSING_KID']:
                    status_code = 401

                response.status_code = status_code
                response.body = json.dumps(self._create_error_response(
                    validation_result['error'],
                    validation_result['error_code'],
                    status_code
                ))
                response.headers['Content-Type'] = 'application/json'
                self.stop()

        except Exception as e:
            # Handle unexpected errors
            self.logger.error(f"Unexpected error in JWT authorizer: {e}", exc_info=True)
            self.metrics['errors'] += 1

            response.status_code = 500
            response.body = json.dumps(self._create_error_response(
                "Internal server error",
                "INTERNAL_ERROR",
                500
            ))
            response.headers['Content-Type'] = 'application/json'
            self.stop()

    def get_metrics(self) -> Dict[str, Any]:
        """
        Get plugin metrics for monitoring.

        Returns:
            Dictionary containing plugin metrics
        """
        metrics = self.metrics.copy()

        # Add cache statistics if validator is initialized
        if self.validator and self.validator.jwks_cache:
            metrics['cache_stats'] = self.validator.jwks_cache.get_cache_stats()

        return metrics


# Plugin instance - this is what APISIX will load
# The class name must match the filename (without .py extension)
JwtAuthorizer = JWTAuthorizer