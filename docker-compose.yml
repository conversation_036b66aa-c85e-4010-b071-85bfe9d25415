services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: cortexa-postgres
    environment:
      POSTGRES_DB: cortexa
      POSTGRES_USER: cortexa_user
      POSTGRES_PASSWORD: cortexa_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cortexa_user -d cortexa"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - cortexa-network

  # Apache Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: cortexa-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - cortexa-network

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: cortexa-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "localhost:9092", "--list"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - cortexa-network

  # Redis for caching and session storage
  redis:
    image: redis:7-alpine
    container_name: cortexa-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - cortexa-network

  # Kafka UI for development
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: cortexa-kafka-ui
    depends_on:
      - kafka
    ports:
      - "8080:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
    networks:
      - cortexa-network

   # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: cortexa-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - monitoring

  # Grafana for visualization
  grafana:
    image: grafana/grafana:10.1.0
    container_name: cortexa-grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - monitoring

  # Tempo for trace storage (alternative to Jaeger)
  tempo:
    image: grafana/tempo:2.2.0
    container_name: cortexa-tempo
    command: [ "-config.file=/etc/tempo.yaml" ]
    volumes:
      - ./monitoring/tempo.yaml:/etc/tempo.yaml
      - tempo_data:/tmp/tempo
    ports:
      - "3200:3200"   # tempo
      - "4317:4317"   # otlp grpc
      - "4318:4318"   # otlp http
    networks:
      - monitoring



  # Voice Gateway Service
  voice-gateway:
    build:
      context: .
      dockerfile: services/voice-gateway/Dockerfile
    container_name: cortexa-voice-gateway
    depends_on:
      kafka:
        condition: service_healthy
    ports:
      - "8002:8002"
    env_file:
      - services/voice-gateway/.env
      - .env.shared
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - cortexa-network
      - monitoring
    restart: unless-stopped


volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
  tempo_data:


networks:
  cortexa-network:
    driver: bridge

  monitoring:
    driver: bridge