# Cortexa Voice Gateway Service

Real-time voice translation service for the Cortexa platform. This service manages stateful, bidirectional WebSocket connections for live speech-to-speech translation.

## Features

- **Real-time WebSocket Communication**: Bidirectional audio streaming
- **Speech-to-Speech Translation Pipeline**: VAD → STT → Translation → TTS
- **Gateway-based Authentication**: Trusts JWT validation from Apache APISIX gateway
- **Fine-grained Authorization**: Role and permission-based access control
- **Concurrent Processing**: Fully asynchronous pipeline handling multiple connections
- **Call Data Persistence**: Automatic transcript and metadata storage with user context
- **Event Publishing**: Kafka integration for call lifecycle events with user information
- **Background Analytics**: ARQ task queue for post-call processing
- **Connection Management**: Automatic cleanup of idle connections
- **Monitoring**: Health checks, metrics, and structured logging

## Authentication & Authorization

The voice-gateway service operates behind an Apache APISIX gateway that handles JWT authentication. The service trusts the identity information provided by the gateway and implements fine-grained authorization based on user roles and permissions.

### Authentication Flow

1. **Client Request**: Client sends WebSocket connection request with <PERSON><PERSON><PERSON> token in Authorization header
2. **Gateway Validation**: APISIX gateway validates JWT using Supabase JWKS
3. **User Context Forwarding**: Gateway forwards validated user information via headers:
   - `X-User-Context`: Base64-encoded user and token information
   - `X-User-ID`: User identifier
   - `X-User-Email`: User email address
   - `X-User-Roles`: JSON array of user roles
4. **Service Authorization**: Voice-gateway parses user context and applies authorization rules
5. **Connection Establishment**: If authorized, WebSocket connection is established with user context

### Authorization Model

#### Roles
- **authenticated**: Basic authenticated user
- **user**: Standard user with call management capabilities
- **premium_user**: Premium user with advanced features
- **admin**: Administrative user with elevated permissions
- **super_admin**: Full system access
- **service_account**: Service-to-service authentication
- **api_client**: API client access

#### Permissions
- **voice:translate**: Basic voice translation
- **voice:translate:premium**: Premium translation features
- **voice:translate:batch**: Batch translation processing
- **call:create**: Create new calls
- **call:view**: View own calls
- **call:view:all**: View all calls (admin)
- **call:delete**: Delete own calls
- **call:export**: Export call data
- **admin:metrics**: View system metrics
- **admin:health**: View system health
- **admin:config**: Modify system configuration
- **analytics:view**: View analytics data
- **analytics:export**: Export analytics data

### Usage Limits

Non-premium users are subject to usage limits:
- Daily translation limit (default: 100 requests)
- Rate limiting per user
- Feature restrictions (no batch processing, premium models)

Premium users have:
- Unlimited daily usage
- Access to premium translation models
- Batch processing capabilities
- Advanced analytics

## Architecture

The service implements a multi-stage asynchronous pipeline:

1. **Reader Task**: Receives audio chunks from WebSocket clients
2. **Processor Task**: Runs the S2ST pipeline (VAD → STT → Translation → TTS)
3. **Writer Task**: Sends translated audio back to clients
4. **Heartbeat Task**: Maintains connection health

## API Endpoints

### WebSocket
- `WS /api/v1/call/ws/call/{call_id}` - Real-time voice translation

### Health
- `GET /health` - Health check with S2ST processor status

## Configuration

The service uses environment variables for configuration. Copy `.env.example` to `.env` and adjust:

### Core Settings
```bash
SERVICE_NAME=voice-gateway
HOST=0.0.0.0
PORT=8002
DEBUG=false
```

### S2ST Pipeline
```bash
WHISPER_MODEL_SIZE=base.en
WHISPER_COMPUTE_TYPE=int8
WHISPER_DEVICE=cpu
VAD_AGGRESSIVENESS=3
TRANSLATION_MODEL=Helsinki-NLP/opus-mt-en-es
TTS_PROVIDER=openai
TTS_API_KEY=your_openai_api_key_here
```

### External Services
```bash
CALL_DATA_SERVICE_URL=http://localhost:8003
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
```

## Installation

### Using Poetry (Development)

```bash
# Install dependencies
poetry install

# Copy environment configuration
cp .env.example .env

# Edit configuration as needed
nano .env

# Run the service
poetry run uvicorn src.main:app --host 0.0.0.0 --port 8002 --reload
```

### Using Docker

```bash
# Build the image
docker build -t voice-gateway .

# Run the container
docker run -p 8002:8002 --env-file .env voice-gateway
```

## Usage

### WebSocket Connection

Connect to the WebSocket endpoint through the APISIX gateway with JWT authentication:

```javascript
const callId = "unique_call_id";
const jwtToken = "your_jwt_token_here"; // Obtained from Supabase auth

// Connect through APISIX gateway (port 9080) with JWT token
const ws = new WebSocket(`ws://localhost:9080/v1/call/ws/call/${callId}`, [], {
    headers: {
        'Authorization': `Bearer ${jwtToken}`
    }
});

ws.onopen = function() {
    console.log("Connected to voice gateway");
};

ws.onmessage = function(event) {
    if (event.data instanceof Blob) {
        // Received translated audio
        playAudio(event.data);
    } else {
        // Received control message
        const message = JSON.parse(event.data);
        console.log("Control message:", message);
    }
};

// Send audio data
function sendAudio(audioBuffer) {
    if (ws.readyState === WebSocket.OPEN) {
        ws.send(audioBuffer);
    }
}
```

### Control Messages

Send JSON control messages for configuration:

```javascript
// Ping/Pong
ws.send(JSON.stringify({type: "ping"}));

// Update configuration
ws.send(JSON.stringify({
    type: "config_update",
    config: {
        target_language: "es"
    }
}));

// End call
ws.send(JSON.stringify({type: "end_call"}));
```

## Dependencies

### Core Dependencies
- **FastAPI**: Web framework and WebSocket support
- **uvicorn**: ASGI server
- **websockets**: WebSocket implementation
- **cortexa-common**: Shared utilities

### S2ST Pipeline
- **faster-whisper**: Speech-to-text transcription
- **py-webrtcvad**: Voice activity detection
- **transformers**: Neural machine translation
- **openai**: Text-to-speech synthesis (or alternative TTS provider)

### Audio Processing
- **numpy**: Numerical computing
- **scipy**: Scientific computing
- **librosa**: Audio analysis
- **soundfile**: Audio file I/O

### Infrastructure
- **arq**: Async task queue
- **redis**: Caching and task queue backend
- **aiokafka**: Kafka event streaming
- **httpx**: HTTP client for service communication

## Development

### Running Tests

```bash
poetry run pytest
```

### Code Quality

```bash
# Format code
poetry run black src/

# Sort imports
poetry run isort src/

# Type checking
poetry run mypy src/

# Linting
poetry run flake8 src/
```

### Adding New Features

1. Follow the domain-driven architecture pattern
2. Add new endpoints in `src/api/v1/endpoints/`
3. Implement business logic in `src/pipeline/`
4. Add schemas in `src/schemas/`
5. Update tests in `tests/`

## Monitoring

### Health Checks

```bash
curl http://localhost:8002/health
```

### Metrics

```bash
curl http://localhost:8002/metrics
```

### Logs

The service uses structured logging with JSON output. Configure log level via the `DEBUG` environment variable.

## Deployment

### Docker Compose

Add to your `docker-compose.yml`:

```yaml
voice-gateway:
  build:
    context: ./services/voice-gateway
    dockerfile: Dockerfile
  container_name: cortexa-voice-gateway
  ports:
    - "8002:8002"
  environment:
    - DATABASE_HOST=postgres
    - REDIS_HOST=redis
    - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
  depends_on:
    - postgres
    - redis
    - kafka
  networks:
    - cortexa-network
  restart: unless-stopped
```

### Production Considerations

1. **Resource Limits**: Set appropriate CPU/memory limits
2. **Scaling**: Use multiple instances behind a load balancer
3. **Security**: Configure CORS, rate limiting, and firewall rules
4. **Monitoring**: Set up Prometheus metrics collection
5. **Logging**: Configure centralized log aggregation
6. **SSL/TLS**: Use HTTPS/WSS in production

## Troubleshooting

### Common Issues

1. **WebSocket Connection Fails**: Check JWT token validity and network connectivity
2. **Audio Quality Issues**: Verify sample rate (16kHz) and format (16-bit mono)
3. **High Latency**: Check model sizes and hardware acceleration settings
4. **Memory Usage**: Monitor connection count and buffer sizes

### Debug Mode

Enable debug mode for detailed logging:

```bash
DEBUG=true poetry run uvicorn src.main:app --reload
```
