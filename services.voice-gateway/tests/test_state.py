
import time
import pytest

from src.pipeline.state import (
    CallState,
    AudioSegment,
    TranscriptEntry,
    ConnectionState,
    ConnectionStateManager,
    connection_state_manager,
)


class TestCallState:
    """Test suite for CallState enumeration."""

    def test_call_state_values(self):
        """Test that CallState enum has correct values."""
        assert CallState.CONNECTING == "connecting"
        assert CallState.ACTIVE == "active"
        assert CallState.PROCESSING == "processing"
        assert CallState.ENDING == "ending"
        assert CallState.ENDED == "ended"
        assert CallState.ERROR == "error"

    def test_call_state_string_representation(self):
        """Test CallState string representation."""
        assert CallState.ACTIVE.value == "active"
        assert CallState.PROCESSING.value == "processing"
        assert CallState.ERROR.value == "error"


class TestAudioSegment:
    """Test suite for AudioSegment class."""

    def test_audio_segment_initialization(self):
        """Test AudioSegment initialization with default values."""
        audio_data = b"fake_audio_data"
        timestamp = time.time()
        duration = 2.5
        
        segment = AudioSegment(audio_data, timestamp, duration)
        
        assert segment.audio_data == audio_data
        assert segment.timestamp == timestamp
        assert segment.duration == duration
        assert segment.sample_rate == 16000  # default
        assert segment.channels == 1  # default
        assert segment.processed is False
        assert segment.transcript is None
        assert segment.translation is None
        assert segment.tts_audio is None

    def test_audio_segment_custom_parameters(self):
        """Test AudioSegment with custom parameters."""
        audio_data = b"custom_audio"
        timestamp = 123456.789
        duration = 5.0
        sample_rate = 48000
        channels = 2
        
        segment = AudioSegment(
            audio_data=audio_data,
            timestamp=timestamp,
            duration=duration,
            sample_rate=sample_rate,
            channels=channels,
        )
        
        assert segment.audio_data == audio_data
        assert segment.timestamp == timestamp
        assert segment.duration == duration
        assert segment.sample_rate == sample_rate
        assert segment.channels == channels

    def test_audio_segment_processing_state(self):
        """Test AudioSegment processing state management."""
        segment = AudioSegment(b"audio", time.time(), 1.0)
        
        # Initially not processed
        assert segment.processed is False
        
        # Set processing results
        segment.transcript = "Hello world"
        segment.translation = "Hola mundo"
        segment.tts_audio = b"tts_audio_data"
        segment.processed = True
        
        assert segment.processed is True
        assert segment.transcript == "Hello world"
        assert segment.translation == "Hola mundo"
        assert segment.tts_audio == b"tts_audio_data"


class TestTranscriptEntry:
    """Test suite for TranscriptEntry dataclass."""

    def test_transcript_entry_initialization(self):
        """Test TranscriptEntry initialization with required fields."""
        timestamp = time.time()
        original_text = "Hello world"
        
        entry = TranscriptEntry(timestamp=timestamp, original_text=original_text)
        
        assert entry.timestamp == timestamp
        assert entry.original_text == original_text
        assert entry.translated_text is None  # default
        assert entry.speaker == "user"  # default
        assert entry.confidence == 0.0  # default

    def test_transcript_entry_full_initialization(self):
        """Test TranscriptEntry with all fields."""
        timestamp = 123456.789
        original_text = "Good morning"
        translated_text = "Buenos días"
        speaker = "operator"
        confidence = 0.95
        
        entry = TranscriptEntry(
            timestamp=timestamp,
            original_text=original_text,
            translated_text=translated_text,
            speaker=speaker,
            confidence=confidence,
        )
        
        assert entry.timestamp == timestamp
        assert entry.original_text == original_text
        assert entry.translated_text == translated_text
        assert entry.speaker == speaker
        assert entry.confidence == confidence


class TestConnectionState:
    """Test suite for ConnectionState dataclass."""

    def test_connection_state_initialization(self, mock_websocket, mock_user):
        """Test ConnectionState initialization."""
        call_id = "test-call-123"
        
        state = ConnectionState(
            websocket=mock_websocket,
            user=mock_user,
            call_id=call_id,
        )
        
        assert state.websocket == mock_websocket
        assert state.user == mock_user
        assert state.call_id == call_id
        assert state.state == CallState.CONNECTING  # default
        assert isinstance(state.connected_at, float)
        assert isinstance(state.last_activity, float)
        assert isinstance(state.audio_buffer, bytearray)
        assert isinstance(state.processed_segments, list)
        assert isinstance(state.transcript, list)
        assert isinstance(state.config, dict)
        assert isinstance(state.errors, list)

    def test_connection_state_with_custom_state(self, mock_websocket, mock_user):
        """Test ConnectionState with custom initial state."""
        state = ConnectionState(
            websocket=mock_websocket,
            user=mock_user,
            call_id="test-call",
            state=CallState.ACTIVE,
        )
        
        assert state.state == CallState.ACTIVE

    def test_update_activity(self, mock_websocket, mock_user):
        """Test activity timestamp update."""
        state = ConnectionState(
            websocket=mock_websocket,
            user=mock_user,
            call_id="test-call",
        )
        
        initial_activity = state.last_activity
        time.sleep(0.01)  # Small delay
        state.update_activity()
        
        assert state.last_activity > initial_activity

    def test_add_audio_chunk(self, mock_websocket, mock_user):
        """Test adding audio chunks to buffer."""
        state = ConnectionState(
            websocket=mock_websocket,
            user=mock_user,
            call_id="test-call",
        )
        
        chunk1 = b"audio_chunk_1"
        chunk2 = b"audio_chunk_2"
        
        initial_activity = state.last_activity
        initial_received = state.total_audio_received
        
        state.add_audio_chunk(chunk1)
        
        assert len(state.audio_buffer) == len(chunk1)
        assert state.total_audio_received == initial_received + len(chunk1)
        assert state.last_activity > initial_activity
        
        # Add second chunk
        state.add_audio_chunk(chunk2)
        
        assert len(state.audio_buffer) == len(chunk1) + len(chunk2)
        assert state.total_audio_received == initial_received + len(chunk1) + len(chunk2)
        assert state.audio_buffer == chunk1 + chunk2

    def test_add_transcript_entry(self, mock_websocket, mock_user):
        """Test adding transcript entries."""
        state = ConnectionState(
            websocket=mock_websocket,
            user=mock_user,
            call_id="test-call",
        )

        state.add_transcript_entry("Hello", "Hola", 0.95)
        assert len(state.transcript) == 1
        assert state.transcript[0].original_text == "Hello"
        assert state.transcript[0].translated_text == "Hola"
        assert state.transcript[0].confidence == 0.95

        state.add_transcript_entry("World")
        assert len(state.transcript) == 2
        assert state.transcript[1].original_text == "World"
        assert state.transcript[1].translated_text is None

    def test_get_stats(self, mock_websocket, mock_user):
        """Test getting connection statistics."""
        state = ConnectionState(
            websocket=mock_websocket,
            user=mock_user,
            call_id="test-call-123",
            state=CallState.ACTIVE,
        )
        
        # Add some test data
        state.add_audio_chunk(b"test_audio")
        state.total_audio_sent = 500
        state.segments_processed = 3
        
        stats = state.get_stats()
        
        assert stats["call_id"] == "test-call-123"
        assert stats["user_id"] == str(mock_user.user_id)
        assert stats["state"] == CallState.ACTIVE.value
        assert stats["connected_at"] == state.connected_at
        assert "duration" in stats
        assert "idle_time" in stats
        assert stats["total_audio_received"] == len(b"test_audio")
        assert stats["total_audio_sent"] == 500
        assert stats["segments_processed"] == 3
        assert stats["transcript_entries"] == 0
        assert stats["errors"] == 0
        assert "buffer_size" in stats
        assert "inbound_queue_size" in stats
        assert "outbound_queue_size" in stats

    def test_add_error(self, mock_websocket, mock_user):
        """Test adding error information."""
        state = ConnectionState(
            websocket=mock_websocket,
            user=mock_user,
            call_id="test-call",
        )

        state.add_error("processing_error", "Failed to process audio", {"detail": "test"})

        assert len(state.errors) == 1
        error = state.errors[0]
        assert error["type"] == "processing_error"
        assert error["message"] == "Failed to process audio"
        assert error["details"]["detail"] == "test"
        assert "timestamp" in error

    def test_get_audio_segment(self, mock_websocket, mock_user):
        """Test extracting audio segments from buffer."""
        state = ConnectionState(
            websocket=mock_websocket,
            user=mock_user,
            call_id="test-call",
        )

        # Add audio to buffer
        audio_data = b"1234567890"
        state.add_audio_chunk(audio_data)

        # Extract segment
        segment = state.get_audio_segment(5)
        assert segment == b"12345"
        assert len(state.audio_buffer) == 5

        # Extract remaining
        segment = state.get_audio_segment(5)
        assert segment == b"67890"
        assert len(state.audio_buffer) == 0

        # Try to extract more than available
        segment = state.get_audio_segment(10)
        assert segment is None

    def test_clear_audio_buffer(self, mock_websocket, mock_user):
        """Test clearing audio buffer."""
        state = ConnectionState(
            websocket=mock_websocket,
            user=mock_user,
            call_id="test-call",
        )

        state.add_audio_chunk(b"test_audio")
        assert len(state.audio_buffer) > 0

        state.clear_audio_buffer()
        assert len(state.audio_buffer) == 0

    def test_connection_duration(self, mock_websocket, mock_user):
        """Test getting connection duration."""
        state = ConnectionState(
            websocket=mock_websocket,
            user=mock_user,
            call_id="test-call",
        )

        duration = state.get_connection_duration()
        assert duration >= 0
        assert isinstance(duration, float)

    def test_idle_time(self, mock_websocket, mock_user):
        """Test getting idle time."""
        state = ConnectionState(
            websocket=mock_websocket,
            user=mock_user,
            call_id="test-call",
        )

        idle_time = state.get_idle_time()
        assert idle_time >= 0
        assert isinstance(idle_time, float)

        # Update activity and check idle time resets
        time.sleep(0.01)
        state.update_activity()
        new_idle_time = state.get_idle_time()
        assert new_idle_time < idle_time

    def test_is_idle(self, mock_websocket, mock_user):
        """Test checking if connection is idle."""
        state = ConnectionState(
            websocket=mock_websocket,
            user=mock_user,
            call_id="test-call",
        )

        # Should not be idle with default timeout
        assert state.is_idle(timeout=300.0) is False

        # Should be idle with very short timeout
        assert state.is_idle(timeout=0.0) is True

    @pytest.mark.asyncio
    async def test_cleanup(self, mock_websocket, mock_user):
        """Test connection cleanup."""
        state = ConnectionState(
            websocket=mock_websocket,
            user=mock_user,
            call_id="test-call",
        )

        # Add some data to queues and buffers
        await state.inbound_queue.put(b"test_audio")
        await state.outbound_queue.put(b"test_response")
        state.add_audio_chunk(b"buffer_data")
        state.processed_segments.append(AudioSegment(b"audio", time.time(), 1.0))

        await state.cleanup()

        assert state.state == CallState.ENDED
        assert len(state.audio_buffer) == 0
        assert len(state.processed_segments) == 0
        assert state.inbound_queue.empty()
        assert state.outbound_queue.empty()


class TestConnectionStateManager:
    """Test suite for ConnectionStateManager."""

    @pytest.mark.asyncio
    async def test_connection_state_manager_initialization(self):
        """Test ConnectionStateManager initialization."""
        manager = ConnectionStateManager()

        assert isinstance(manager._connections, dict)
        assert len(manager._connections) == 0

    @pytest.mark.asyncio
    async def test_add_connection(self, mock_websocket, mock_user):
        """Test adding a connection to the manager."""
        manager = ConnectionStateManager()
        
        state = ConnectionState(
            websocket=mock_websocket,
            user=mock_user,
            call_id="test-call-123",
        )
        
        await manager.add_connection(state)

        assert "test-call-123" in manager._connections
        assert manager._connections["test-call-123"] == state

    @pytest.mark.asyncio
    async def test_remove_connection(self, mock_websocket, mock_user):
        """Test removing a connection from the manager."""
        manager = ConnectionStateManager()
        
        state = ConnectionState(
            websocket=mock_websocket,
            user=mock_user,
            call_id="test-call-123",
        )
        
        await manager.add_connection(state)
        assert "test-call-123" in manager._connections

        await manager.remove_connection("test-call-123")
        assert "test-call-123" not in manager._connections

    @pytest.mark.asyncio
    async def test_get_connection(self, mock_websocket, mock_user):
        """Test getting a connection from the manager."""
        manager = ConnectionStateManager()
        
        # Test getting nonexistent connection
        result = await manager.get_connection("nonexistent")
        assert result is None
        
        # Add connection and test getting it
        state = ConnectionState(
            websocket=mock_websocket,
            user=mock_user,
            call_id="test-call-123",
        )
        
        await manager.add_connection(state)
        result = await manager.get_connection("test-call-123")
        
        assert result == state

    @pytest.mark.asyncio
    async def test_get_all_connections(self, mock_websocket, mock_user):
        """Test getting all connections from the manager."""
        manager = ConnectionStateManager()
        
        # Initially empty
        connections = await manager.get_all_connections()
        assert len(connections) == 0
        
        # Add connections
        state1 = ConnectionState(websocket=mock_websocket, user=mock_user, call_id="call-1")
        state2 = ConnectionState(websocket=mock_websocket, user=mock_user, call_id="call-2")
        
        await manager.add_connection(state1)
        await manager.add_connection(state2)
        
        connections = await manager.get_all_connections()
        assert len(connections) == 2
        assert state1 in connections
        assert state2 in connections

    @pytest.mark.asyncio
    async def test_get_connection_count(self, mock_websocket, mock_user):
        """Test getting connection count from the manager."""
        manager = ConnectionStateManager()
        
        # Initially zero
        count = await manager.get_connection_count()
        assert count == 0
        
        # Add connections
        state1 = ConnectionState(websocket=mock_websocket, user=mock_user, call_id="call-1")
        state2 = ConnectionState(websocket=mock_websocket, user=mock_user, call_id="call-2")
        
        await manager.add_connection(state1)
        count = await manager.get_connection_count()
        assert count == 1
        
        await manager.add_connection(state2)
        count = await manager.get_connection_count()
        assert count == 2

    def test_global_connection_state_manager(self):
        """Test that global connection state manager instance exists."""
        assert connection_state_manager is not None
        assert isinstance(connection_state_manager, ConnectionStateManager)
