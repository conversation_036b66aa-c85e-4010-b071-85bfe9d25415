from typing import Optional

from cortexacommon.logging import get_logger
from cortexacommon.events.producer import EventProducer
from cortexacommon.config import KafkaSettings

from ..schemas.events import BaseEvent

logger = get_logger(__name__)


class EventPublisher:
    """Enhanced event publisher for voice-gateway events."""
    
    def __init__(self):
        """Initialize the event publisher."""
        self._producer = None
    
    async def initialize(self) -> None:
        """Initialize Kafka producer."""
        kafka_settings = KafkaSettings()

        self._producer = EventProducer(kafka_settings)
        await self._producer.start()

        logger.info("Event publisher initialized")

    async def cleanup(self) -> None:
        """Cleanup the event publisher."""
        if not self._producer:
            return

        try:
            await self._producer.stop()
            logger.info("Event publisher cleaned up")
        except Exception as e:
            logger.error(f"Error during event publisher cleanup: {e}")
        finally:
            self._producer = None
    
    async def publish_event(self, event: BaseEvent, topic: Optional[str] = None) -> None:
        """Publish an event to Kafka."""
        assert self._producer is not None, "Event publisher not initialized"

        # Use event type as topic if not specified
        if topic is None:
            topic = f"voice-gateway.{event.event_type.replace('.', '-')}"

        event.source_service = "voice-gateway"
        
        # Publish event
        await self._producer.publish_event(topic, event)
        logger.debug(f"Published event {event.event_type} to topic {topic}")
