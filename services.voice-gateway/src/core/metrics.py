"""
Prometheus metrics for voice-gateway service.

This module defines custom metrics to monitor voice processing pipeline performance.
"""

from fastapi import FastAPI
from cortexacommon.logging import get_logger
from prometheus_client import Counter, Histogram, Gauge, Info
from prometheus_fastapi_instrumentator import Instrumentator

logger = get_logger(__name__)

# Connection metrics
active_connections = Gauge(
    'voice_gateway_active_connections',
    'Number of active WebSocket connections'
)

total_connections = Counter(
    'voice_gateway_total_connections',
    'Total number of WebSocket connections created'
)


def setup_metrics(app: FastAPI) -> None:
    """
    Set up Prometheus metrics for the FastAPI application.

    Args:
        app: FastAPI application instance
    """

    # Initialize FastAPI instrumentator
    instrumentator = Instrumentator(
        excluded_handlers=["/metrics"],
        should_group_status_codes=True,
        should_ignore_untemplated=True,
        should_instrument_requests_inprogress=True,
        inprogress_name="http_requests_inprogress",
        inprogress_labels=True,
    )
    instrumentator.instrument(app, metric_subsystem="voice_gateway")

    logger.info("Exposing metrics endpoint at /metrics...")
    instrumentator.expose(app, endpoint="/metrics")

    logger.info("Prometheus metrics initialized successfully")


class MetricsCollector:
    """Helper class for collecting custom metrics."""
    
    @staticmethod
    def record_connection_opened(call_id: str):
        """Record a new WebSocket connection."""
        total_connections.inc()
        active_connections.inc()
    
    @staticmethod
    def record_connection_closed(call_id: str):
        """Record a closed WebSocket connection."""
        active_connections.dec()
