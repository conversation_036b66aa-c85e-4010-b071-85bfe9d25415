from typing import Optional, Dict, Any, List
from pydantic import Field
from cortexacommon.events.schemas import BaseEvent

class CallStartedEvent(BaseEvent):
    """Event published when a voice call starts."""
    
    event_type: str = Field(default="call.started", description="Event type")
    call_id: str = Field(description="Unique call identifier")
    user_id: str = Field(description="User identifier")
    source_language: Optional[str] = Field(default=None, description="Source language code")
    target_language: Optional[str] = Field(default=None, description="Target language code")
    providers: Dict[str, str] = Field(description="Selected providers for STT, TTS, translation")


class CallEndedEvent(BaseEvent):
    """Event published when a voice call ends."""
    
    event_type: str = Field(default="call.ended", description="Event type")
    call_id: str = Field(description="Unique call identifier")
    duration_seconds: float = Field(description="Call duration in seconds")
    total_segments: int = Field(description="Total speech segments processed")
    total_audio_received_bytes: int = Field(description="Total audio bytes received")
    total_audio_sent_bytes: int = Field(description="Total audio bytes sent")
    error_count: int = Field(description="Number of errors during the call")
    final_transcript: List[Dict[str, Any]] = Field(description="Complete call transcript")
