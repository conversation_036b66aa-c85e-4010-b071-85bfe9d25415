import logging

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, WebSocket, WebSocketDisconnect
from fastapi.websockets import WebSocketState

from cortexacommon.tracing import TracingMixin
from cortexacommon.security.gateway import AuthenticatedUser, get_authenticated_user_from_headers

from src.core.context import Application<PERSON>ontext, get_app_context
from src.pipeline.state import ConnectionState, CallState, connection_state_manager
from src.pipeline.tasks import run_pipeline
from src.schemas.events import CallEndedEvent

logger = logging.getLogger(__name__)
router = APIRouter()


class WebSocketUnauthenticatedError(Exception):
    """Exception raised when WebSocket authentication fails."""


class WebSocketHandler(TracingMixin):
    """WebSocket handler with tracing capabilities."""

    def __init__(self, context: ApplicationContext):
        self.context = context
        super().__init__()

    async def handle_websocket(self, websocket: WebSocket, call_id: str) -> None:
        try:
            await websocket.accept()

            authenticated_user = await self.authenticate(websocket)
            logger.info(f"Authenticated user {authenticated_user.user_id} connecting to call {call_id}")
            
            await self.process_state(websocket, call_id)
            
        except WebSocketUnauthenticatedError:     
            await websocket.close(code=4001, reason="Authentication required")
            logger.warning(f"Connection rejected for call {call_id}: no authentication provided")

    async def authenticate(self, websocket: WebSocket) -> AuthenticatedUser | None:
        headers_dict = dict(websocket.headers)
        authenticated_user = get_authenticated_user_from_headers(headers_dict)
        if not authenticated_user:
            raise WebSocketUnauthenticatedError("Authentication required")

        return authenticated_user

    async def process_state(self, websocket: WebSocket, call_id: str) -> None:
        """Implementation of WebSocket connection handling."""
        state = ConnectionState(
            websocket=websocket,
            call_id=call_id,
            authenticated_user=authenticated_user,
            state=CallState.ACTIVE,
        )

        await connection_state_manager.add_connection(state)

        try:
            # Send initial connection confirmation
            await websocket.send_json({
                "type": "connection_established",
                "call_id": call_id,
                "message": "Voice translation session started",
                "timestamp": state.connected_at,
            })

            # Start the main processing pipeline with tracing context
            with self.create_span("websocket.pipeline", call_id=call_id) as pipeline_span:
                await run_pipeline(state, self.context.s2st_processor)
                pipeline_span.set_attribute("result", "completed")

        except WebSocketDisconnect:
            logger.info(f"Client for call {call_id} disconnected")
            state.state = CallState.ENDING
        except Exception as e:
            logger.error(f"Error in WebSocket pipeline for call {call_id}: {str(e)}")
            state.state = CallState.ERROR
            state.add_error("pipeline_error", str(e))
        finally:
            await self.process_termination(state)

    async def process_termination(self, state: ConnectionState) -> None:
        """ Handle call termination and cleanup. """
        logger.info(f"Terminating call {state.call_id}")
        
        try:
            # Update state
            state.state = CallState.ENDING
            
            # Publish CallEnded event to Kafka
            await self.context.events_publisher.publish_event(
                event=CallEndedEvent(
                    call_id=state.call_id,
                    duration_seconds=state.get_connection_duration(),
                    total_segments=len(state.transcript),
                    total_audio_received_bytes=state.total_audio_received_bytes,
                    total_audio_sent_bytes=state.total_audio_sent_bytes,
                    error_count=len(state.errors),
                    final_transcript=state.transcript,
                )
            )
            
            # Send final message to client if connection is still open
            if state.websocket.client_state == WebSocketState.CONNECTED:
                try:
                    await state.websocket.send_json({
                        "type": "call_ended",
                        "call_id": state.call_id,
                        "message": "Call ended successfully",
                        "stats": state.get_stats(),
                    })
                except Exception:
                    pass  # Connection might be closed
            
        except Exception as e:
            logger.error(f"Error during call termination for {state.call_id}: {str(e)}")
            state.add_error("termination_error", str(e))
        
        finally:
            await state.cleanup()
            await connection_state_manager.remove_connection(state.call_id)
            state.state = CallState.ENDED
            
            logger.info(f"Call {state.call_id} terminated and cleaned up")


@router.websocket("/ws/call/{call_id}")
async def websocket_endpoint(
    websocket: WebSocket,
    call_id: str,
    context: ApplicationContext = Depends(get_app_context),
):
    context.tracer.start_span("websocket.connection", call_id=call_id)

    logger.info(f"New WebSocket connection attempt for call {call_id}")
    await WebSocketHandler(context).handle_websocket(websocket, call_id)

    context.tracer.end_span()

